import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    experimental: {
        esmExternals: "loose",
    },
    webpack: (config, { isServer }) => {
        // Handle node modules that need to be transpiled
        config.module.rules.push({
            test: /\.m?js$/,
            type: "javascript/auto",
            resolve: {
                fullySpecified: false,
            },
        });

        // Handle leaflet CSS imports
        config.module.rules.push({
            test: /\.css$/,
            use: ["style-loader", "css-loader"],
        });

        return config;
    },
    transpilePackages: ["shpjs", "@turf/turf"],
};

export default nextConfig;
